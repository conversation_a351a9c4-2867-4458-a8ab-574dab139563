# Mobile App CRUSH.md

This document outlines the essential commands, code style, and conventions for the mobile_app codebase.

## Project Setup

- **Get dependencies**: `flutter pub get`
- **Run the app**: `flutter run`

## Development Commands

- **Build**:
  - **Android**: `flutter build apk`
  - **iOS**: `flutter build ios`
- **Lint**: `flutter analyze`
- **Test**:
  - **Run all tests**: `flutter test`
  - **Run a single test file**: `flutter test test/widget_test.dart`

## Code Style & Conventions

- **Formatting**: Use `flutter format .` to format all files.
- **Imports**: Organize imports alphabetically.
- **Naming**:
  - Use `camelCase` for variables and functions.
  - Use `PascalCase` for classes.
- **Error Handling**: Use `try-catch` blocks for network calls and platform-specific code.
- **State Management**: The project uses `flutter_bloc` for state management. Follow BLoC patterns and conventions.
- **Localization**: Use the `easy_localization` package for all user-facing strings.
- **Assets**: All assets are located in the `assets/` directory.

## App Flavors

The app supports multiple flavors for different environments (e.g., dev, prod). To run a specific flavor, use the `--flavor` flag:

- `flutter run --flavor <flavor_name>`
