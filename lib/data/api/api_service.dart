import 'dart:io';

//import 'package:dio/adapter.dart';
import 'package:dio/dio.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/curl_logger.dart';
import '../../utils/dio_cache.dart';
import 'api_constants.dart';
import 'environment.dart';
import 'interceptors.dart';

class ApiService {
  static ApiService? _instance;

  factory ApiService() => _instance ??= ApiService._();

  ApiService._();

  static const String USER_AGENT = "user-agent";
  static const _timeout = 600000;

  bool get isInDebugMode {
    bool inDebugMode = false;
    assert(inDebugMode = true);
    return inDebugMode;
  }

  //Switch the environment between Prod and Dev
  Environment _env = _Prod();
  Environment get env => _env;

  Dio get dio => _dio();
  Dio _dio() {
    final options = BaseOptions(
      baseUrl: _env.baseUrl,
      //connectTimeout: _timeout,
      //receiveTimeout: _timeout,
      connectTimeout: Duration(seconds: 600000),
      receiveTimeout: Duration(seconds: 600000),
    );

    var dio = Dio(options);

    //TODO: This code use for ignore http client certificate //hide code then go live
    /*(dio.httpClientAdapter as DefaultHttpClientAdapter).onHttpClientCreate = (HttpClient client) {
    client.badCertificateCallback = (X509Certificate cert, String host, int port) => true;
    return client;};*/

    // TODO: This code for test api
    // if (Platform.isAndroid) {
    //   // Make sure to replace <YOUR_LOCAL_IP> with
    //   // the external IP of your computer if you're using Android.
    //   // You can get the IP in the Android Setup Guide window
    //   String proxy =
    //       Platform.isAndroid ? '***********:9090' : 'localhost:9090';

    //   // Tap into the onHttpClientCreate callback
    //   // to configure the proxy just as we did earlier.
    //   (dio.httpClientAdapter as DefaultHttpClientAdapter).onHttpClientCreate =
    //       (client) {
    //     // Hook into the findProxy callback to set the client's proxy.
    //     client.findProxy = (url) {
    //       return 'PROXY $proxy';
    //     };

    //     // This is a workaround to allow Proxyman to receive
    //     // SSL payloads when your app is running on Android.
    //     client.badCertificateCallback =
    //         (X509Certificate cert, String host, int port) => Platform.isAndroid;
    //     return null;
    //   };
    // }

    // TODO:Live
    dio.interceptors.add(CustomCacheInterceptor());
    dio.interceptors.add(CurlLoggerDioInterceptor(printOnSuccess: true));
    dio.interceptors.add(requestInterceptor(dio, _env));
    return dio;
  }

  void setEnvironment(EnvironmentType type) {
    Log.v("Setting environment to $type");
    switch (type) {
      case EnvironmentType.DEV:
        _env = _Dev();
        break;
      default:
        _env = _Prod();
    }
  }
}

class _Prod extends Environment {
  @override
  EnvironmentType get type => EnvironmentType.PROD;

  @override
  String get baseUrl => ApiConstants().PRODUCTION_BASE_URL();

  @override
  String get apiKey => "For api key";
}

class _Dev extends Environment {
  @override
  EnvironmentType get type => EnvironmentType.DEV;

  @override
  String get baseUrl => ApiConstants.DEV_BASE_URL;

  @override
  String get apiKey => "For api key";
}

enum ApiStatus { INITIAL, LOADING, SUCCESS, ERROR }

enum NotificationStatus { initial, loading, loadingMore, success, error }
